<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试页面 - 网页爬虫</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: #409eff;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .content {
            padding: 20px;
        }
        .item {
            border: 1px solid #e4e7ed;
            border-radius: 6px;
            margin-bottom: 16px;
            padding: 16px;
            background: #fafafa;
            transition: all 0.3s ease;
        }
        .item:hover {
            background: #f0f9ff;
            border-color: #409eff;
        }
        .item-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        .item-title a {
            color: #409eff;
            text-decoration: none;
        }
        .item-title a:hover {
            text-decoration: underline;
        }
        .item-meta {
            color: #909399;
            font-size: 14px;
            margin-bottom: 8px;
        }
        .item-content {
            color: #606266;
            line-height: 1.6;
        }
        .pagination {
            text-align: center;
            padding: 20px;
            border-top: 1px solid #e4e7ed;
            background: #fafafa;
        }
        .page-link {
            display: inline-block;
            padding: 8px 12px;
            margin: 0 4px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            color: #606266;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        .page-link:hover {
            background: #409eff;
            color: white;
            border-color: #409eff;
        }
        .page-link.current {
            background: #409eff;
            color: white;
            border-color: #409eff;
        }
        .next-btn {
            background: #67c23a;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-left: 10px;
            transition: all 0.3s ease;
        }
        .next-btn:hover {
            background: #5daf34;
        }
        .author {
            font-weight: 600;
            color: #409eff;
        }
        .time {
            color: #909399;
        }
        .replies {
            background: #f56c6c;
            color: white;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>测试论坛 - 第1页</h1>
            <p>这是一个用于测试网页爬虫的示例页面</p>
        </div>
        
        <div class="content">
            <div class="item">
                <div class="item-title">
                    <a href="/topic/1">Vue 3 + TypeScript 开发实践分享</a>
                </div>
                <div class="item-meta">
                    作者: <span class="author">张三</span> | 
                    时间: <span class="time">2024-06-24 10:30</span> | 
                    回复: <span class="replies">25</span>
                </div>
                <div class="item-content">
                    分享一些在Vue 3和TypeScript结合开发中的实践经验，包括组合式API的使用技巧、类型定义的最佳实践等。
                </div>
            </div>

            <div class="item">
                <div class="item-title">
                    <a href="/topic/2">前端性能优化的几个关键点</a>
                </div>
                <div class="item-meta">
                    作者: <span class="author">李四</span> | 
                    时间: <span class="time">2024-06-24 09:15</span> | 
                    回复: <span class="replies">18</span>
                </div>
                <div class="item-content">
                    从代码分割、懒加载、缓存策略等多个角度讨论前端性能优化的方法和实践。
                </div>
            </div>

            <div class="item">
                <div class="item-title">
                    <a href="/topic/3">React vs Vue：2024年该如何选择？</a>
                </div>
                <div class="item-meta">
                    作者: <span class="author">王五</span> | 
                    时间: <span class="time">2024-06-24 08:45</span> | 
                    回复: <span class="replies">42</span>
                </div>
                <div class="item-content">
                    对比分析React和Vue在2024年的发展趋势、生态系统、学习曲线等方面的差异。
                </div>
            </div>

            <div class="item">
                <div class="item-title">
                    <a href="/topic/4">Node.js 微服务架构设计思考</a>
                </div>
                <div class="item-meta">
                    作者: <span class="author">赵六</span> | 
                    时间: <span class="time">2024-06-24 07:20</span> | 
                    回复: <span class="replies">33</span>
                </div>
                <div class="item-content">
                    探讨在Node.js环境下如何设计和实现微服务架构，包括服务拆分、通信机制、监控等。
                </div>
            </div>

            <div class="item">
                <div class="item-title">
                    <a href="/topic/5">CSS Grid 布局完全指南</a>
                </div>
                <div class="item-meta">
                    作者: <span class="author">孙七</span> | 
                    时间: <span class="time">2024-06-23 22:10</span> | 
                    回复: <span class="replies">15</span>
                </div>
                <div class="item-content">
                    详细介绍CSS Grid布局的各种属性和使用场景，帮助开发者掌握现代CSS布局技术。
                </div>
            </div>

            <div class="item">
                <div class="item-title">
                    <a href="/topic/6">JavaScript 异步编程最佳实践</a>
                </div>
                <div class="item-meta">
                    作者: <span class="author">周八</span> | 
                    时间: <span class="time">2024-06-23 20:30</span> | 
                    回复: <span class="replies">28</span>
                </div>
                <div class="item-content">
                    从Promise到async/await，深入讲解JavaScript异步编程的各种模式和最佳实践。
                </div>
            </div>

            <div class="item">
                <div class="item-title">
                    <a href="/topic/7">Docker 容器化部署实战</a>
                </div>
                <div class="item-meta">
                    作者: <span class="author">吴九</span> | 
                    时间: <span class="time">2024-06-23 18:45</span> | 
                    回复: <span class="replies">21</span>
                </div>
                <div class="item-content">
                    分享使用Docker进行应用容器化部署的实际经验，包括Dockerfile编写、镜像优化等。
                </div>
            </div>

            <div class="item">
                <div class="item-title">
                    <a href="/topic/8">GraphQL API 设计与实现</a>
                </div>
                <div class="item-meta">
                    作者: <span class="author">郑十</span> | 
                    时间: <span class="time">2024-06-23 16:20</span> | 
                    回复: <span class="replies">19</span>
                </div>
                <div class="item-content">
                    介绍GraphQL的核心概念和优势，以及如何设计和实现高效的GraphQL API。
                </div>
            </div>
        </div>

        <div class="pagination">
            <a href="?page=1" class="page-link current">1</a>
            <a href="?page=2" class="page-link">2</a>
            <a href="?page=3" class="page-link">3</a>
            <a href="?page=4" class="page-link">4</a>
            <a href="?page=5" class="page-link">5</a>
            
            <button class="next-btn" onclick="goToNextPage()" title="下一页">
                下一页 →
            </button>
        </div>
    </div>

    <script>
        function goToNextPage() {
            // 模拟跳转到下一页
            const currentPage = 1;
            const nextPage = currentPage + 1;
            window.location.href = `?page=${nextPage}`;
        }
        
        // 模拟动态加载效果
        document.addEventListener('DOMContentLoaded', function() {
            const items = document.querySelectorAll('.item');
            items.forEach((item, index) => {
                item.style.opacity = '0';
                item.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    item.style.transition = 'all 0.5s ease';
                    item.style.opacity = '1';
                    item.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
