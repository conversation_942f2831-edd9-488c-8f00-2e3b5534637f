/**
 * 分页处理器 - 处理页面翻页逻辑
 */
export class PaginationHandler {
  constructor(config) {
    this.config = config;
    this.currentPage = 1;
    this.totalPages = null;
    this.visitedUrls = new Set();
  }

  /**
   * 检测分页信息
   */
  detectPagination() {
    const pagination = {
      hasNext: false,
      nextUrl: null,
      nextElement: null,
      currentPage: this.getCurrentPage(),
      totalPages: this.getTotalPages(),
      pageLinks: this.getPageLinks()
    };

    // 检测下一页按钮
    const nextButton = this.findNextButton();
    if (nextButton) {
      pagination.hasNext = true;
      pagination.nextElement = nextButton.element;
      pagination.nextUrl = this.getNextUrl(nextButton.element);
    }

    return pagination;
  }

  /**
   * 查找下一页按钮
   */
  findNextButton() {
    const selectors = this.config.paginationSelectors.nextButton;
    
    for (const selector of selectors) {
      try {
        let element;
        
        // 处理包含文本的选择器
        if (selector.includes(':contains(')) {
          const text = selector.match(/:contains\("([^"]+)"\)/)?.[1];
          if (text) {
            const elements = document.querySelectorAll('a, button, .button');
            element = Array.from(elements).find(el => 
              el.textContent.trim().includes(text) && 
              !el.classList.contains('disable_now') &&
              !el.disabled
            );
          }
        } else {
          element = document.querySelector(selector);
        }
        
        if (element && this.isValidNextButton(element)) {
          return {
            selector,
            element
          };
        }
      } catch (error) {
        console.warn(`选择器 "${selector}" 执行失败:`, error);
      }
    }
    
    return null;
  }

  /**
   * 验证下一页按钮是否有效
   */
  isValidNextButton(element) {
    // 检查是否被禁用
    if (element.disabled || element.classList.contains('disabled') || 
        element.classList.contains('disable_now')) {
      return false;
    }
    
    // 检查是否有有效的链接或点击事件
    const hasHref = element.href && element.href !== '#';
    const hasOnclick = element.onclick || element.getAttribute('onclick');
    
    return hasHref || hasOnclick;
  }

  /**
   * 获取下一页URL
   */
  getNextUrl(element) {
    // 优先使用href属性
    if (element.href && element.href !== '#') {
      return element.href;
    }
    
    // 解析onclick事件
    const onclick = element.onclick || element.getAttribute('onclick');
    if (onclick) {
      const urlMatch = onclick.toString().match(/location\.href\s*=\s*['"]([^'"]+)['"]/);
      if (urlMatch) {
        const url = urlMatch[1];
        return url.startsWith('http') ? url : new URL(url, window.location.origin).href;
      }
    }
    
    return null;
  }

  /**
   * 获取当前页码
   */
  getCurrentPage() {
    // 从URL参数获取
    const urlParams = new URLSearchParams(window.location.search);
    const pageFromUrl = urlParams.get('p') || urlParams.get('page');
    if (pageFromUrl) {
      return parseInt(pageFromUrl, 10);
    }
    
    // 从页面元素获取
    const currentPageElement = document.querySelector('.page_current, .current, .active');
    if (currentPageElement) {
      const pageNum = parseInt(currentPageElement.textContent.trim(), 10);
      if (!isNaN(pageNum)) {
        return pageNum;
      }
    }
    
    return 1;
  }

  /**
   * 获取总页数
   */
  getTotalPages() {
    // 从页码输入框获取
    const pageInput = document.querySelector('.page_input, input[type="number"]');
    if (pageInput) {
      const max = parseInt(pageInput.getAttribute('max'), 10);
      if (!isNaN(max)) {
        return max;
      }
    }
    
    // 从页码链接获取
    const pageLinks = document.querySelectorAll('.page_normal, .page-link');
    if (pageLinks.length > 0) {
      const lastPageLink = pageLinks[pageLinks.length - 1];
      const pageNum = parseInt(lastPageLink.textContent.trim(), 10);
      if (!isNaN(pageNum)) {
        return pageNum;
      }
    }
    
    return null;
  }

  /**
   * 获取所有页码链接
   */
  getPageLinks() {
    const links = [];
    const selectors = this.config.paginationSelectors.pageLinks;
    
    for (const selector of selectors) {
      const elements = document.querySelectorAll(selector);
      if (elements.length > 0) {
        elements.forEach(element => {
          const pageNum = parseInt(element.textContent.trim(), 10);
          if (!isNaN(pageNum) && element.href) {
            links.push({
              page: pageNum,
              url: element.href,
              element
            });
          }
        });
        break; // 找到一组有效的页码链接就停止
      }
    }
    
    return links.sort((a, b) => a.page - b.page);
  }

  /**
   * 导航到下一页
   */
  async goToNextPage() {
    const pagination = this.detectPagination();
    
    if (!pagination.hasNext) {
      throw new Error('没有找到下一页');
    }
    
    const nextUrl = pagination.nextUrl;
    if (!nextUrl) {
      throw new Error('无法获取下一页URL');
    }
    
    // 检查是否已经访问过这个URL
    if (this.visitedUrls.has(nextUrl)) {
      throw new Error('检测到循环，停止爬取');
    }
    
    // 记录当前URL
    this.visitedUrls.add(window.location.href);
    
    // 导航到下一页
    if (pagination.nextElement.href) {
      // 使用链接跳转
      window.location.href = nextUrl;
    } else if (pagination.nextElement.onclick) {
      // 使用点击事件
      pagination.nextElement.click();
    } else {
      // 直接跳转
      window.location.href = nextUrl;
    }
    
    // 等待页面加载
    return new Promise((resolve) => {
      const checkPageLoad = () => {
        if (document.readyState === 'complete') {
          setTimeout(resolve, this.config.crawlSettings.delay);
        } else {
          setTimeout(checkPageLoad, 100);
        }
      };
      checkPageLoad();
    });
  }

  /**
   * 检查是否可以继续翻页
   */
  canContinue(currentPageCount, maxPages) {
    // 检查最大页数限制
    if (maxPages && currentPageCount >= maxPages) {
      return false;
    }
    
    // 检查是否有下一页
    const pagination = this.detectPagination();
    if (!pagination.hasNext) {
      return false;
    }
    
    // 检查是否已经到达最后一页
    if (pagination.totalPages && pagination.currentPage >= pagination.totalPages) {
      return false;
    }
    
    return true;
  }

  /**
   * 获取分页统计信息
   */
  getPaginationStats() {
    const pagination = this.detectPagination();
    
    return {
      currentPage: pagination.currentPage,
      totalPages: pagination.totalPages,
      hasNext: pagination.hasNext,
      pageLinksCount: pagination.pageLinks.length,
      visitedUrlsCount: this.visitedUrls.size
    };
  }

  /**
   * 重置分页状态
   */
  reset() {
    this.currentPage = 1;
    this.totalPages = null;
    this.visitedUrls.clear();
  }
}
