/**
 * 配置管理器 - 管理爬虫的配置信息
 */
export class ConfigManager {
  constructor() {
    this.defaultConfig = {
      // 列表项选择器配置
      listSelectors: [
        '.cell.item',           // V2EX样式
        '.item',                // 通用item类
        'li',                   // 列表项
        '.list-item',           // 通用列表项
        '.post',                // 帖子类
        '.article',             // 文章类
        'tr',                   // 表格行
      ],
      
      // 分页选择器配置
      paginationSelectors: {
        nextButton: [
          'a[title="下一页"]',
          '.next',
          '.page-next',
          'a:contains("下一页")',
          'a:contains("Next")',
          'a:contains(">")',
          '.normal_page_right',   // V2EX样式
        ],
        pageLinks: [
          '.page_normal',         // V2EX样式
          '.page-link',
          '.pagination a',
          'a[href*="p="]',
          'a[href*="page="]',
        ]
      },
      
      // 数据提取配置
      extractionRules: {
        title: [
          '.item_title a',        // V2EX样式
          '.title',
          'h1', 'h2', 'h3',
          '.post-title',
          '.article-title',
        ],
        link: [
          '.item_title a',
          '.title a',
          'a[href]',
        ],
        author: [
          '.topic_info strong a', // V2EX样式
          '.author',
          '.username',
          '.user',
        ],
        time: [
          '.topic_info span[title]', // V2EX样式
          '.time',
          '.date',
          '.timestamp',
        ],
        replies: [
          '.count_livid',         // V2EX样式
          '.reply-count',
          '.comments',
        ],
        content: [
          '.content',
          '.description',
          '.summary',
          'p',
        ]
      },
      
      // 爬取设置
      crawlSettings: {
        delay: 2000,            // 页面间延迟(ms)
        maxPages: 10,           // 最大页数
        timeout: 30000,         // 超时时间(ms)
        retryCount: 3,          // 重试次数
      }
    };
  }

  /**
   * 获取配置
   */
  getConfig() {
    const savedConfig = this.loadConfig();
    return { ...this.defaultConfig, ...savedConfig };
  }

  /**
   * 保存配置
   */
  saveConfig(config) {
    try {
      if (typeof GM_setValue !== 'undefined') {
        GM_setValue('crawlerConfig', JSON.stringify(config));
      } else {
        localStorage.setItem('crawlerConfig', JSON.stringify(config));
      }
    } catch (error) {
      console.error('保存配置失败:', error);
    }
  }

  /**
   * 加载配置
   */
  loadConfig() {
    try {
      let configStr;
      if (typeof GM_getValue !== 'undefined') {
        configStr = GM_getValue('crawlerConfig', '{}');
      } else {
        configStr = localStorage.getItem('crawlerConfig') || '{}';
      }
      return JSON.parse(configStr);
    } catch (error) {
      console.error('加载配置失败:', error);
      return {};
    }
  }

  /**
   * 重置为默认配置
   */
  resetConfig() {
    this.saveConfig({});
    return this.defaultConfig;
  }

  /**
   * 更新特定配置项
   */
  updateConfig(key, value) {
    const config = this.getConfig();
    config[key] = value;
    this.saveConfig(config);
    return config;
  }

  /**
   * 根据当前页面自动检测最佳配置
   */
  autoDetectConfig() {
    const detectedConfig = {};
    
    // 检测列表容器
    const listContainer = this.detectListContainer();
    if (listContainer) {
      detectedConfig.detectedListSelector = listContainer.selector;
      detectedConfig.detectedItemCount = listContainer.count;
    }
    
    // 检测分页元素
    const pagination = this.detectPagination();
    if (pagination) {
      detectedConfig.detectedPagination = pagination;
    }
    
    return detectedConfig;
  }

  /**
   * 检测列表容器
   */
  detectListContainer() {
    const config = this.getConfig();
    
    for (const selector of config.listSelectors) {
      const elements = document.querySelectorAll(selector);
      if (elements.length >= 2) { // 至少2个元素才认为是列表
        return {
          selector,
          count: elements.length,
          elements: Array.from(elements)
        };
      }
    }
    
    return null;
  }

  /**
   * 检测分页元素
   */
  detectPagination() {
    const config = this.getConfig();
    const result = {};
    
    // 检测下一页按钮
    for (const selector of config.paginationSelectors.nextButton) {
      const element = document.querySelector(selector);
      if (element) {
        result.nextButton = {
          selector,
          element,
          href: element.href || element.onclick
        };
        break;
      }
    }
    
    // 检测页码链接
    for (const selector of config.paginationSelectors.pageLinks) {
      const elements = document.querySelectorAll(selector);
      if (elements.length > 0) {
        result.pageLinks = {
          selector,
          count: elements.length,
          elements: Array.from(elements)
        };
        break;
      }
    }
    
    return Object.keys(result).length > 0 ? result : null;
  }
}
